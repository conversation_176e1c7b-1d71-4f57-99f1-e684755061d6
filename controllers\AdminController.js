const { Admin, Customer, Category, Order, Product, DeliveryPerson, AdminToken, Notification,OrderDetail } = require('../models');
const { Op } = require('sequelize');

class AdminController {
  // Admin Dashboard
  async dashboard(req, res) {
    try {
      const [customers, admin, categories, orders,orderspending, products, deliveryPeople,notifications] = await Promise.all([
        Customer.count(),
        Admin.findByPk(req.user.id),
        Category.count(),
        Order.count(),
        Order.count({
          where: {
            status: 'pending'
          }
        }),
        Product.count(),
        DeliveryPerson.count(),
        Notification.count({
          where: {
            adminId: req.user.id,
            readAt: null
          }
        })
      ]);

         const todayOrders = await Order.findAll({
            where: {
                createdAt: {
                    [Op.gte]: new Date(new Date().setHours(0, 0, 0, 0))
                }
            }
          });
          const totalValue = todayOrders.reduce((sum, order) => sum + parseFloat(order.totalPrice || 0), 0);

          const stats = {
              totalorders: todayOrders.length,
              todayPending: await Order.count({
                  where: {
                      status: 'pending',
                      createdAt: {
                          [Op.gte]: new Date(new Date().setHours(0, 0, 0, 0))
                      }
                  }
              }),
              totalValue,
              todaycustomers: await Customer.count({
                  where: {
                      createdAt: {
                          [Op.gte]: new Date(new Date().setHours(0, 0, 0, 0))
                      }
                  }
              })
          };


      res.render('admin/dashboard', {
        stats,
        customers,
        admin,
        categories,
        orders,
        orderspending,
        products,
        deliveryPeople,
        notifications
      });
    } catch (error) {
      res.status(500).render('error', { error });
    }
  }

  // controllers/adminController.js

  async profile (req, res) {
    try {
      const admin = await Admin.findByPk(req.user.id, {
        attributes: { exclude: ['password'] }
      });
      const stats = {
        totalOrders: await Order.count(),
        totalCustomers: await Customer.count(),
        totalProducts: await Product.count(),
        totalRevenue: await Order.sum('totalPrice')
      };

      const ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress;

      res.render('admin/auth/profile', {
        title: 'الملف الشخصي',
        admin,
        stats,
        ip
      });
    } catch (error) {
      console.error('Error loading profile:', error);
      res.status(500).render('error', {
        error: { message: 'حدث خطأ أثناء تحميل الملف الشخصي' }
      });
    }
  };

  async editProfile(req, res) {
    try {
      const admin = await Admin.findByPk(req.user.id, {
        attributes: { exclude: ['password'] }
      });
      res.render('admin/auth/edit-profile', {
        title: 'تعديل الملف الشخصي',
        admin
      });
    } catch (error) {
      console.error('Error loading edit profile form:', error);
      res.status(500).render('error', {
        error: { message: 'حدث خطأ أثناء تحميل النموذج' }
      });
    }
  }

  async updateProfile(req, res) {
    try {
        const { fullName, username, email } = req.body;

        await Admin.update(
          { fullName, username, email },
          { where: { id: req.admin.id } }
        );

        req.flash('success', 'تم تحديث المعلومات بنجاح');
        res.redirect('/admin/profile');
    } catch (err) {
      console.error(err);
      req.flash('error', 'حدث خطأ أثناء التحديث');
      res.redirect('/admin/edit-profile');
    }
  }

 
}

module.exports = new AdminController();
