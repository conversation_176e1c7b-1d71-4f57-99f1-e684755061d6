const { Delivery<PERSON>erson, Delivery,  DeliveryP<PERSON>ple<PERSON><PERSON>,Customer } = require('../models');
const path = require('path');
const bcrypt = require('bcryptjs');
const deliverypersonarea = require('../models/deliverypersonarea');
const fs = require('fs');
// عرض جميع السائقين  
exports.index = async (req, res) => {
      const limit = 20;
      let currentPage = parseInt(req.query.page) || 1;
      if (currentPage < 1) currentPage = 1;
      const offset = (currentPage - 1) * limit;

      try {
        const { count, rows: deliveryPeople } = await DeliveryPerson.findAndCountAll({
          include: [
            {
              model: Delivery,
              as: 'deliveries',
              required: false
            }
          ],
          limit,
          offset,
          distinct: true, // 👈 هذا مهم جدًا
          order: [['createdAt', 'DESC']]
        });

        const totalPages = Math.ceil(count / limit);

        res.render('admin/drivers/index', {
          deliveryPeople,
          currentPage,
          totalPages,
          count // اختياري
        });
      } catch (error) {
        console.error("Error fetching delivery people:", error);
        res.status(500).render('error', {
          error: { message: 'Unable to fetch delivery people' }
        });
      }

};

// عرض صفحة إضافة سائق
exports.createForm = (req, res) => {
  res.render('admin/drivers/create');
};

// إنشاء سائق جديد
exports.create = async (req, res) => {
  try {
    const { name,username, password, phoneNumber, regions } = req.body;

       // التحقق من عدم وجود عميل بنفس رقم الهاتف
            const existingCustomer = await Customer.findOne({ where: { phoneNumber } });
            const existingDriver = await DeliveryPerson.findOne({ where: { phoneNumber } });
            if (existingCustomer || existingDriver) {
              console.log('يوجد سائق مسجل بهذا رقم الهاتف');
                return res.status(400).render('admin/drivers/create', {
                    error: 'يوجد سائق مسجل بهذا رقم الهاتف',
                    formData: req.body
                });
            }

            const existingName = await Customer.findOne({ where: { name : username } });
            const existingNameDriver = await DeliveryPerson.findOne({ where: { username } });
            if (existingName || existingNameDriver) {
              console.log('اسم السائق مستخدم بالفعل');
                return res.status(400).render('admin/drivers/create', {
                    error: 'اسم السائق مستخدم بالفعل',
                    formData: req.body
                });
            }
    // صورة السائق
    let imagePath = null;
    if (req.file) {
      imagePath = `/uploads/deliveries/${req.file.filename}`;
    }
    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(password, 10);
    // إنشاء السائق
    const driver = await DeliveryPerson.create({
      name,
      username,
      password : hashedPassword,
      phoneNumber,
      image: imagePath,
    });

    // إرسال إشعار للمدراء عن السائق الجديد
    try {
      const FirebaseMessagingService = require('../services/FirebaseMessagingService');

      const notification = {
        title: 'سائق توصيل جديد',
        body: `تم إضافة سائق توصيل جديد: ${name} - ${phoneNumber}`
      };

      const data = {
        driverId: driver.id.toString(),
        driverName: name,
        driverPhone: phoneNumber,
        clickAction: `/admin/drivers/${driver.id}`,
        type: 'new_driver'
      };

      const result = await FirebaseMessagingService.sendToAllAdmins(notification, data);
    } catch (notificationError) {
      console.error('❌ Error sending driver notification:', notificationError);
      // لا نوقف العملية إذا فشل الإشعار
    }

    // تأكد أن المناطق مصفوفة
    const regionArray = Array.isArray(regions) ? regions : Object.values(regions);

    // حفظ المناطق وربطها بالسائق
    for (const regionData of regionArray) {
      await DeliveryPeopleArea.create({
        deliveryPeople_id: driver.id,
        city: regionData.city,
        region: regionData.region,
        address: regionData.address,
        notes: regionData.notes || null
      });
    }

    req.flash('success', 'تمت إضافة السائق بنجاح');
    res.redirect('/admin/drivers');
  } catch (error) {
    console.error('فشل في إضافة السائق:', error);
    req.flash('error', 'حدث خطأ أثناء إضافة السائق');
    res.redirect('back');
  }
};

// عرض صفحة تعديل سائق
exports.editForm = async (req, res) => {
const deliveryPerson = await DeliveryPerson.findByPk(req.params.id, {
  include: [
    {
      model: DeliveryPeopleArea,
      as: 'areas'
    }
  ]
});
res.render('admin/drivers/edit', { deliveryPerson });
};

exports.update = async (req, res) => {
  try {
    const driverId = req.params.id;
    const { name,username,password, phoneNumber, status } = req.body;

    const driver = await DeliveryPerson.findByPk(driverId);
    if (!driver) return res.status(404).send('السائق غير موجود');

    const existingCustomer = await Customer.findOne({ where: { phoneNumber } });
    const existingPhone = await DeliveryPerson.findOne({
      where: {
        phoneNumber,
        id: { [Op.ne]: driverId } // تجاهل السجل الحالي
      }
    });
    if (existingCustomer || existingPhone) {
      return res.status(400).send('رقم الهاتف مستخدم بالفعل لسائق آخر');
    }


    const existingName = await Customer.findOne({ where: { name : username } });
    const existingNameDriver = await DeliveryPerson.findOne({ where: { username, id: { [Op.ne]: driverId } } });
    if (existingName || existingNameDriver) {
      console.log('اسم السائق مستخدم بالفعل');
        return res.status(400).render('admin/drivers/create', {
            error: 'اسم السائق مستخدم بالفعل',
            formData: req.body
        });
    }
    // تحديث البيانات الأساسية
    driver.name = name;
    driver.username = username;
     // تحديث كلمة المرور إذا تم إدخالها
    if (password && password.trim() !== '') {
        driver.password = await bcrypt.hash(password, 10);
    }
    driver.phoneNumber = phoneNumber;
    driver.status = status;

    // إذا كانت هناك صورة جديدة مرفوعة
    if (req.file) {
      // حذف الصورة القديمة إذا كانت موجودة
      if (driver.image) {
        const oldPath = path.join(__dirname, '..', 'public', driver.image);
        if (fs.existsSync(oldPath)) {
          fs.unlinkSync(oldPath);
        }
      }

      // حفظ اسم الصورة الجديدة
      driver.image = `/uploads/deliveries/${req.file.filename}`;
    }

    await driver.save();

    // حذف جميع المناطق القديمة وإضافة الجديدة
    await DeliveryPeopleArea.destroy({ where: { deliveryPeople_id: driver.id } });

    const regions = req.body.regions || {};
    const regionsArray = Object.values(regions);

    for (const region of regionsArray) {
      await DeliveryPeopleArea.create({
        deliveryPeople_id: driver.id,
        city: region.city,
        region: region.region,
        address: region.address,
        latitude: region.latitude,
        longitude: region.longitude
      });
    }

    res.redirect('/admin/drivers'); // أو أي مسار رجوع
  } catch (err) {
    console.error(err);
    res.status(500).send('حدث خطأ أثناء التحديث');
  }
};

exports.delete = async (req, res) => {
  const { id } = req.params;

  try {
    await DeliveryPerson.destroy({ where: { id } });
    res.redirect('/admin/drivers');
  } catch (error) {
    console.error('خطأ أثناء حذف السائق:', error);
    res.status(500).send('حدث خطأ أثناء حذف السائق');
  }
};

exports.getProfileDriver = async (req, res) => {
  try {
    const driverId = req.params.id;
    const driver = await DeliveryPerson.findByPk(driverId, {
      attributes: { exclude: ['password'] }
    });

    if (!driver) {
      return res.status(404).render('error', { error: { message: 'Driver not found' } });
    }

    res.render('admin/drivers/profile', { driver });
    } catch (error) {
      console.error('Error loading driver profile:', error);
      res.status(500).render('error', { error: { message: 'حدث خطأ أثناء تحميل الملف الشخصي' } });
    }
  };
