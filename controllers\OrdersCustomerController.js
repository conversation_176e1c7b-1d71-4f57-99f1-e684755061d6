const BaseController = require('./BaseController');
const { Order, Customer, Product, OrderDetail, Category, Delivery, deliveryPerson, sequelize } = require('../models');
const { buildSearchAndFilter, buildSortOptions, buildPaginationOptions, calculatePaginationInfo, sanitizeFilters } = require('../utils/searchFilter');
const { Op } = require('sequelize');
const NotificationsFunction = require('./NotificationsFunction');
const logger = require('../utils/logger');

class OrdersController extends BaseController {
    constructor() {
        super(Order, 'orders');
    }

    async processAddCheckout(req, res) {
        const transaction = await sequelize.transaction();
        try {
            const customerId = req.customer?.id;
            if (!customerId) {
                return res.status(401).json({
                    success: false,
                    message: 'غير مصرح لك',
                    data: null
                });
            }
            const customer = await Customer.findByPk(customerId);
            if (customer.address === null || customer.city === null || customer.rating === null) {
                await transaction.rollback();
                return res.status(400).json({
                    success: false,
                    message: 'يرجى إكمال بيانات العنوان (العنوان، المدينة)',
                    data: null
                });
            }
            const  items  = req.body;

            if (!Array.isArray(items) || items.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: 'المشتريات مطلوبة',
                    data: null
                });
            }

            let totalPrice = 0;
            const orderDetails = [];

            for (const item of items) {
                // تحقق من وجود المنتج فعلياً في قاعدة البيانات
                const product = await Product.findByPk(item.id);

                if (!product) {
                    await transaction.rollback();
                    return res.status(400).json({
                        success: false,
                        message: `المنتج بالمعرف ${item.id} غير موجود`,
                        data: { productId: item.id }
                    });
                }

                // تحقق من توفر الكمية
                if (product.quantity < item.quntity) {
                    await transaction.rollback();
                    return res.status(400).json({
                        success: false,
                        message: `الكمية غير متوفرة للمنتج ${product.name}`,
                        data: {
                            productId: product.id,
                            available: product.quantity,
                            requested: item.quntity
                        }
                    });
                }

                const itemPrice = parseFloat(item.Price);
                const itemQuantity = parseInt(item.quntity);

                if (isNaN(itemPrice) || isNaN(itemQuantity)) {
                    await transaction.rollback();
                    return res.status(400).json({
                        success: false,
                        message: `السعر أو الكمية غير صالحة للمنتج ${product.name}`,
                        data: { productId: product.id }
                    });
                }
                const itemTotal = itemPrice * itemQuantity;
                totalPrice += itemTotal;

                orderDetails.push({
                    productId: product.id,
                    quantity: item.quntity,
                    totalPrice: itemTotal,
                    notes : item.notes
                });
    /*
                // يمكنك إنقاص الكمية إذا رغبت
                await Product.update(
                    { quantity: product.quantity - item.quntity },
                    { where: { id: product.id }, transaction }
                );*/
            }

            // إنشاء الطلب الرئيسي
            const order = await Order.create({
                customerId,
                totalPrice,
                status: 'pending',
                deliveryAddress :customer.address,
                notes: null
            }, { transaction });

            // ربط التفاصيل بالطلب
            const fullDetails = orderDetails.map(detail => ({
                ...detail,
                orderId: order.id
            }));

            await OrderDetail.bulkCreate(fullDetails, { transaction });
            NotificationsFunction.sendNewOrderNotifications(order);

            await transaction.commit();

            return res.status(201).json({
                success: true,
                message: 'تم إنشاء الطلب بنجاح',
                data: {
                    orderId: order.id,
                    status: order.status,
                    totalPrice: totalPrice,
                    totalItems: orderDetails.length
                }
            });

        } catch (error) {
            await transaction.rollback();
            console.error('خطأ في معالجة الطلب:', error);
            res.status(500).json({
                success: false,
                message: error.message || 'حدث خطأ أثناء تنفيذ الطلب',
                data: null
            });
        }
    }

    async getOrders(req, res) {
        try {
            const customerId = req.customer?.id;
            if (!customerId) {
                return res.status(401).json({
                    success: false,
                    message: 'غير مصرح لك',
                    data: null
                });
            }

            const {
                page = 1,
                limit = 10,
                status = null,
                startDate = null,
                endDate = null
            } = req.query;

            const offset = (page - 1) * limit;
            const whereClause = { customerId: customerId };

            // فلتر بالحالة
            if (status) {
                whereClause.status = status;
            }else{
                 whereClause.status = {
                    [Op.in]: ['pending', 'processing', 'out_for_delivery']
                };
            }

            // فلتر بالتاريخ
            if (startDate || endDate) {
                whereClause.createdAt = {};
                if (startDate) whereClause.createdAt[Op.gte] = new Date(startDate);
                if (endDate) whereClause.createdAt[Op.lte] = new Date(endDate);
            }

            const { count, rows: orders } = await Order.findAndCountAll({
                where: whereClause,
                attributes: ['id', 'customerId','totalPrice','status', 'deliveryAddress','rating','notes','createdAt','deliveryTime'],
                include: [
                    {
                        model: OrderDetail,
                        as: 'orderDetails',
                        include: [
                            {
                                model: Product,
                                as: 'product',
                                attributes: ['id', 'name', 'price'],
                                include: [
                                    {
                                        model: Category,
                                        as: 'category',
                                        attributes: ['id', 'name']
                                    }
                                ]
                            }
                        ]
                    }
                ],
                limit: parseInt(limit),
                offset: parseInt(offset),
                order: [['createdAt', 'DESC']]
            });

            // تنسيق البيانات
            const formattedOrders = orders.map(order => ({
                id: order.id,
                totalPrice: parseFloat(order.totalPrice),
                status: order.status,
                address: order.deliveryAddress,
                createdAt: order.createdAt,
                notes: order.notes,
                ProductsOrder: order.orderDetails ? order.orderDetails.map(detail => ({
                    id: detail.id,
                    name: detail.product.name,
                    category: detail.product.category ? detail.product.category.name : null,
                    quantity: detail.quantity,
                    price: parseFloat(detail.product.price),
                    totalPrice: parseFloat(detail.totalPrice),
                    notes: detail.notes,
                })) : [],
               // itemsCount: order.orderDetails ? order.orderDetails.length : 0
            }));

            res.json({
                success: true,
                message: `تم جلب ${formattedOrders.length} طلب بنجاح`,
                data: {
                    orders: formattedOrders,
                   /* pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }*/
                }
            });

        } catch (error) {
            console.error('خطأ في جلب الطلبات:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    async getOrdersCompleted(req, res) {
        try {
            const customerId = req.customer?.id;
           if (!customerId) {
                return res.status(401).json({
                    success: false,
                    message: 'غير مصرح لك',
                    data: null
                });
            }

            const {
                page = 1,
                limit = 10,
                status = null,
                startDate = null,
                endDate = null
            } = req.query;

            const offset = (page - 1) * limit;
            const whereClause = { customerId: customerId };

            // فلتر بالحالة
            if (status) {
                whereClause.status = status;
            }else{
                whereClause.status = {
                    [Op.in]: ['completed', 'cancelled', 'rejected']
                };
            }

            // فلتر بالتاريخ
            if (startDate || endDate) {
                whereClause.createdAt = {};
                if (startDate) whereClause.createdAt[Op.gte] = new Date(startDate);
                if (endDate) whereClause.createdAt[Op.lte] = new Date(endDate);
            }

            const { count, rows: orders } = await Order.findAndCountAll({
                where: whereClause,
                attributes: ['id', 'customerId','totalPrice','status', 'deliveryAddress','rating','notes','createdAt','deliveryTime'],
                include: [
                    {
                        model: OrderDetail,
                        as: 'orderDetails',
                        include: [
                            {
                                model: Product,
                                as: 'product',
                                attributes: ['id', 'name', 'price'],
                                include: [
                                    {
                                        model: Category,
                                        as: 'category',
                                        attributes: ['id', 'name']
                                    }
                                ]
                            }
                        ]
                    }
                ],
                limit: parseInt(limit),
                offset: parseInt(offset),
                order: [['createdAt', 'DESC']]
            });

            // تنسيق البيانات
            const formattedOrders = orders.map(order => ({
                id: order.id,
                totalPrice: parseFloat(order.totalPrice),
                status: order.status,
                address: order.deliveryAddress,
                createdAt: order.createdAt,
                notes: order.notes,
                deliveryTime: order.deliveryTime,
                ProductsOrder: order.orderDetails ? order.orderDetails.map(detail => ({
                    id: detail.id,
                    name: detail.product.name,
                    category: detail.product.category ? detail.product.category.name : null,
                    quantity: detail.quantity,
                    price: parseFloat(detail.product.price),
                    totalPrice: parseFloat(detail.totalPrice),
                    notes: detail.notes,
                })) : [],
               // itemsCount: order.orderDetails ? order.orderDetails.length : 0
            }));

            res.json({
                success: true,
                message: `تم جلب ${formattedOrders.length} طلب بنجاح`,
                data: {
                    orders: formattedOrders,
                   /* pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }*/
                }
            });

        } catch (error) {
            console.error('خطأ في جلب الطلبات:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    async completedorders(req, res) {
        try {
            const { id } = req.params;
            const order = await Order.findByPk(id);
            if (!order) {
                return res.status(401).json({
                        success: false,
                        message: 'الطلب غير موجود',
                        data: null
                    });
            }else if (order.status !== 'out_for_delivery') {
                return res.status(422).json({
                        success: false,
                        message: 'لا يمكن تغيير حالة الطلب',
                        data: null
                    });
            }
            await order.update({ status : 'completed' , deliveryTime: new Date() });
            const delivery = await Delivery.findOne({ where: { orderId: order.id } });
            if (delivery) {
                await delivery.update({ status: 'delivered', deliveryTime: new Date() });
            }

            NotificationsFunction.completedOrderNotifications(order);
            res.json({
                    success: true,
                    message: 'تم تغيير حالة الطلب بنجاح',
                    data: null
            });
        } catch (error) {
            console.error('حدث خطأ في الخادم', error);
            res.status(500).json({
                        success: false,
                        message: 'حدث خطأ في الخادم',
                        data: null
            });
        }
    } 

    async canceledorders(req, res) {
        try {
            const { id } = req.params;
            const order = await Order.findByPk(id);
            if (!order) {
                return res.status(401).json({
                        success: false,
                        message: 'الطلب غير موجود',
                        data: null
                    });
            }else if (order.status !== 'pending') {
                return res.status(422).json({
                        success: false,
                        message: 'لا يمكن تغيير حالة الطلب',
                        data: null
                    });
            }
            await order.update({ status : 'cancelled' });

            NotificationsFunction.canceledOrderNotifications(order);
            res.json({
                    success: true,
                    message: 'تم تغيير حالة الطلب بنجاح',
                    data: null
            });
        } catch (error) {
            console.error('حدث خطأ في الخادم', error);
            res.status(500).json({
                        success: false,
                        message: 'حدث خطأ في الخادم',
                        data: null
            });
        }
    }
}

module.exports = new OrdersController();
