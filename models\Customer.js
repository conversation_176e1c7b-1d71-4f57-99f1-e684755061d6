'use strict';
const { Model } = require('sequelize');
const bcrypt = require('bcryptjs');

module.exports = (sequelize, DataTypes) => {
  class Customer extends Model {
    static associate(models) {
      Customer.hasMany(models.Order, {
        foreignKey: 'customerId',
        as: 'orders'
      });

      Customer.hasMany(models.CustomerToken, {
        foreignKey: 'customerId',
        as: 'tokens'
      });

      Customer.hasMany(models.Favorite, {
        foreignKey: 'customer_id',
        as: 'favorites'
      });
    }

    async validatePassword(password) {
      return bcrypt.compare(password, this.password);
    }
  }

  Customer.init({
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    barcode: DataTypes.STRING,
    password: {
      type: DataTypes.STRING,
      allowNull: false,
      set(value) {
        // Hash password before saving
        const hashedPassword = bcrypt.hashSync(value, 10);
        this.setDataValue('password', hashedPassword);
      }
    },
     image: {
      type: DataTypes.STRING,
      allowNull: true
    },
    phoneNumber: DataTypes.STRING,
    discountRate: DataTypes.DECIMAL(10, 2),
    notes: DataTypes.TEXT,
    address: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'عنوان العميل'
    },
    city: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'المدينة'
    },
    region: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'المنطقة'
    },
    latitude: {
      type: DataTypes.DECIMAL(10, 8),
      allowNull: true,
      comment: 'خط العرض'
    },
    longitude: {
      type: DataTypes.DECIMAL(11, 8),
      allowNull: true,
      comment: 'خط الطول'
    },
    currentToken: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive'),
      defaultValue: 'active',
      allowNull: false
    }
  }, {
    sequelize,
    modelName: 'Customer',
  });

  return Customer;
};