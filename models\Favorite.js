'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Favorite extends Model {
    static associate(models) {
      Favorite.belongsTo(models.Customer, {
        foreignKey: 'customer_id',
        as: 'customer'
      });
      Favorite.belongsTo(models.Product, {
        foreignKey: 'product_id',
        as: 'product'
      });
    }
  }

  Favorite.init({
    customer_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Customers', // اسم الجدول في قاعدة البيانات
        key: 'id'
      }
    },
    product_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Products',
        key: 'id'
      }
    }
  }, {
    sequelize,
    modelName: 'Favorite',
    tableName: 'Favorites', // التصحيح هنا
    timestamps: false // إذا لم تكن تستخدم createdAt و updatedAt
  });

  return Favorite;
};
