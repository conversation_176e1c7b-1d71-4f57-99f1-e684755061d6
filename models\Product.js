'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Product extends Model {
    static associate(models) {
      Product.belongsTo(models.Category, {
        foreignKey: 'categoryId',
        as: 'category'
      });
      Product.hasMany(models.OrderDetail, {
        foreignKey: 'productId',
        as: 'orderDetails'
      });
      Product.hasMany(models.Image, {
        foreignKey: 'productId',
        as: 'images'
      });
      Product.hasMany(models.Favorite, {
        foreignKey: 'product_id',
        as: 'favorites'
      });
      Product.hasMany(models.Offer, {
        foreignKey: 'productId',
        as: 'offers'
      });
    }
  }

  Product.init({
    categoryId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Categories',
        key: 'id'
      }
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    description: DataTypes.TEXT,
    barcode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive'),
      allowNull: false,
      defaultValue: 'active'
    },
    rating: DataTypes.INTEGER,
    notes: DataTypes.TEXT
  }, {
    sequelize,
    modelName: 'Product'
  });

  return Product;
};
