'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class DeliveryPerson extends Model {
    static associate(models) {
      // مندوب التوصيل يمكن أن يقوم بعدة سجلات توصيل
      DeliveryPerson.hasMany(models.Delivery, {
        foreignKey: 'deliveryPersonId', as: 'deliveries'
      });
      DeliveryPerson.hasMany(models.DeliveryPeopleArea, {
        foreignKey: 'deliveryPeople_id',
        as: 'areas'
      });
    }
  }

  DeliveryPerson.init({
    name: { type: DataTypes.STRING, allowNull: false },
    username: { type: DataTypes.STRING, allowNull: false, unique: true },
    password: { type: DataTypes.STRING, allowNull: false },
    phoneNumber: DataTypes.STRING,
     image: {
      type: DataTypes.STRING,
      allowNull: true
    },
    currentToken: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM('active','inactive'),
      defaultValue: 'active'
    }
  }, {
    sequelize,
    modelName: 'DeliveryPerson',
    tableName: 'DeliveryPeople'
  });

  return DeliveryPerson;
};