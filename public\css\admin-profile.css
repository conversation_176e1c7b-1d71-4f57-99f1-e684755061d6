/* ===== أنماط صفحة بروفايل الأدمن ===== */

/* متغيرات إضافية للبروفايل */
:root {
    --profile-gradient: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    --card-hover-shadow: 0 15px 35px rgba(14, 165, 233, 0.2);
    --stat-card-gradient: linear-gradient(135deg, var(--primary-500), var(--primary-600));
}

/* حاوي البروفايل */
.profile-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

/* رأس البروفايل */
.profile-header {
    background: var(--profile-gradient);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.profile-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid rgba(255, 255, 255, 0.3);
    margin: 0 auto 1rem;
    position: relative;
    z-index: 1;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: rgba(255, 255, 255, 0.8);
    overflow: hidden;
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.profile-name {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 1;
}

.profile-role {
    font-size: 1.1rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

/* بطاقات البروفايل */
.profile-card {
    background: var(--white);
    border: 2px solid var(--primary-200);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.profile-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-hover-shadow);
}

.profile-card-title {
    color: var(--primary-600);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* شبكة المعلومات */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.info-item {
    padding: 1rem;
    background: var(--primary-50);
    border-radius: 10px;
    border-left: 4px solid var(--primary-500);
    transition: all var(--transition-normal);
}

.info-item:hover {
    transform: translateX(-5px);
    background: var(--primary-100);
}

.info-label {
    font-weight: 600;
    color: var(--primary-700);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 500;
}

/* شبكة الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.stat-card {
    background: var(--stat-card-gradient);
    color: white;
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.stat-card:hover::before {
    left: 100%;
}

.stat-card:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 10px 25px rgba(14, 165, 233, 0.3);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 1;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.btn-profile {
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 500;
    text-decoration: none;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: none;
    cursor: pointer;
}

.btn-primary-profile {
    background: var(--profile-gradient);
    color: white;
}

.btn-primary-profile:hover {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
    box-shadow: var(--shadow-md);
}

.btn-secondary-profile {
    background: var(--primary-100);
    color: var(--primary-700);
    border: 2px solid var(--primary-200);
}

.btn-secondary-profile:hover {
    background: var(--primary-200);
    color: var(--primary-800);
    text-decoration: none;
    transform: translateY(-2px);
}

/* عناصر النشاط */
.activity-item {
    padding: 1rem;
    border-bottom: 1px solid var(--primary-100);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all var(--transition-normal);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item:hover {
    background: var(--primary-50);
    transform: translateX(-5px);
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-100);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-600);
    transition: all var(--transition-normal);
}

.activity-item:hover .activity-icon {
    background: var(--primary-200);
    transform: scale(1.1);
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

body {
  background-color: #f1f3f5;
  font-family: 'Cairo', sans-serif;
  direction: rtl;
  margin: 0;
  padding: 0;
}

.profile-wrapper {
  max-width: 950px;
  margin: 50px auto;
  background: #fff;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 6px 20px rgba(0,0,0,0.08);
}

.profile-top {
  display: flex;
  align-items: center;
  gap: 20px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 20px;
  margin-bottom: 30px;
}

.avatar {
  width: 100px;
  height: 100px;
  background: #f5f5f5;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-icon {
  font-size: 48px;
  color: #999;
}

.admin-info h2 {
  font-size: 26px;
  margin: 0;
  color: #333;
}

.admin-info p {
  margin: 4px 0 0;
  color: #666;
  font-size: 16px;
}

.profile-section {
  margin-bottom: 40px;
}

.profile-section h3 {
  font-size: 20px;
  color: #444;
  margin-bottom: 16px;
  border-right: 4px solid #007bff;
  padding-right: 10px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 14px 30px;
  font-size: 16px;
  color: #444;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 20px;
}

.stat-box {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  text-align: center;
  transition: 0.3s;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.stat-box:hover {
  background: #e9f2ff;
  transform: translateY(-4px);
}

.stat-box h4 {
  margin: 0;
  font-size: 26px;
  color: #007bff;
}

.stat-box p {
  margin-top: 6px;
  color: #666;
  font-size: 15px;
}

.badge.success {
  background-color: #28a745;
  color: #fff;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 14px;
}

.profile-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 20px;
}

.btn {
  background-color: #007bff;
  color: #fff;
  padding: 10px 20px;
  border-radius: 8px;
  text-decoration: none;
  font-size: 15px;
  font-weight: 600;
  transition: background 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn:hover {
  background-color: #0056b3;
}

.btn.secondary {
  background-color: #6c757d;
}

.btn.secondary:hover {
  background-color: #4e555b;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .profile-container {
        padding: 1rem;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .btn-profile {
        width: 100%;
        justify-content: center;
    }
    
    .profile-name {
        font-size: 1.5rem;
    }
    
    .profile-avatar {
        width: 100px;
        height: 100px;
        font-size: 2.5rem;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-number {
        font-size: 2rem;
    }
}
