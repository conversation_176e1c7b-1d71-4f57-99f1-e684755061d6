<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>تعديل البروفايل</title>
  <link rel="stylesheet" href="/css/admin-profile.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
  <style>
    .form-wrapper {
      max-width: 700px;
      margin: 40px auto;
      background: #fff;
      border-radius: 12px;
      padding: 30px;
      box-shadow: 0 6px 16px rgba(0,0,0,0.08);
    }

    .form-wrapper h2 {
      margin-bottom: 20px;
      color: #333;
    }

    .form-group {
      margin-bottom: 18px;
    }

    label {
      display: block;
      margin-bottom: 6px;
      color: #444;
      font-weight: 500;
    }

    input {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #ccc;
      border-radius: 8px;
      font-size: 16px;
    }

    .btn-submit {
      margin-top: 20px;
      background-color: #007bff;
      color: #fff;
      padding: 10px 22px;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      transition: 0.3s;
    }

    .btn-submit:hover {
      background-color: #0056b3;
    }

    .back-link {
      display: inline-block;
      margin-top: 20px;
      color: #007bff;
      text-decoration: none;
      font-size: 15px;
    }

    .back-link:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>

<div class="form-wrapper">
  <h2><i class="fas fa-edit"></i> تعديل معلومات البروفايل</h2>
  <form action="/admin/update-profile" method="POST">
    <div class="form-group">
      <label for="fullName">الاسم الكامل</label>
      <input type="text" id="fullName" name="fullName" value="<%= admin.fullName || '' %>" required>
    </div>

    <div class="form-group">
      <label for="username">اسم المستخدم</label>
      <input type="text" id="username" name="username" value="<%= admin.username || '' %>" required>
    </div>

    <div class="form-group">
      <label for="email">البريد الإلكتروني</label>
      <input type="email" id="email" name="email" value="<%= admin.email || '' %>" required>
    </div>

    <button type="submit" class="btn-submit">حفظ التعديلات</button>
  </form>

  <a href="/admin/profile" class="back-link"><i class="fas fa-arrow-right"></i> الرجوع للبروفايل</a>
</div>

</body>
</html>
