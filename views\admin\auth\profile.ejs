  <title>الملف الشخصي</title>
  <link rel="stylesheet" href="/css/admin-profile.css">


<div class="profile-wrapper">
  <!-- القسم العلوي -->
  <div class="profile-top">
    <div class="avatar">
      <% if (admin?.image) { %>
        <img src="<%= admin.image %>" alt="صورة الأدمن">
      <% } else { %>
        <i class="fas fa-user-shield default-icon"></i>
      <% } %>
    </div>
    <div class="admin-info">
      <h2><%= admin?.username || 'مدير النظام' %></h2>
      <p>مدير عام</p>
    </div>
  </div>

  <!-- المعلومات الشخصية -->
  <div class="profile-section">
    <h3><i class="fas fa-user"></i> المعلومات الشخصية</h3>
    <div class="info-grid">
      <div><strong>الاسم الكامل:</strong> <%= admin?.fullName || 'غير محدد' %></div>
      <div><strong>البريد الإلكتروني:</strong> <%= admin?.email || 'غير محدد' %></div>
    </div>
  </div>

  <!-- الإحصائيات -->
  <div class="profile-section">
    <h3><i class="fas fa-chart-bar"></i> الإحصائيات</h3>
    <div class="stats-grid">
      <div class="stat-box">
        <h4><%= stats?.totalOrders || 0 %></h4>
        <p>إجمالي الطلبات</p>
      </div>
      <div class="stat-box">
        <h4><%= stats?.totalCustomers || 0 %></h4>
        <p>العملاء</p>
      </div>
      <div class="stat-box">
        <h4><%= stats?.totalProducts || 0 %></h4>
        <p>المنتجات</p>
      </div>
      <div class="stat-box">
        <h4><%= stats?.totalRevenue?.toLocaleString('ar-EG') || '0' %> ل.س</h4>
        <p>الإيرادات</p>
      </div>
    </div>
  </div>

  <!-- الأمان -->
  <div class="profile-section">
    <h3><i class="fas fa-shield-alt"></i> معلومات الأمان</h3>
    <div class="info-grid">
      <div><strong>آخر دخول:</strong> <%= admin?.lastLogin ? new Date(admin.lastLogin).toLocaleString('ar-EG') : 'غير متاح' %></div>
      <div><strong>IP:</strong> <%= ip || 'غير معروف' %></div>
      <div><strong>الحالة:</strong> <span class="badge success">نشط</span></div>
      <div><strong>الدور:</strong> مدير عام</div>
    </div>
  </div>

  <!-- الإجراءات -->
  <div class="profile-actions">
    <a href="/admin/edit-profile" class="btn"><i class="fas fa-edit"></i> تعديل البيانات</a>
    <a href="/admin/auth/change-password" class="btn secondary"><i class="fas fa-key"></i> تغيير كلمة المرور</a>
    <a href="/admin/dashboard" class="btn secondary"><i class="fas fa-home"></i> لوحة التحكم</a>
  </div>
</div>

