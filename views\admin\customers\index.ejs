
<style>
 .page-header {
        background: var(--white);
        padding: 2rem;
        border-radius: 16px;
        box-shadow: var(--shadow);
        margin-bottom: 2rem;
    }
</style>

<div class="container">
         <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-2">
                    <i class="fas fa-users text-primary me-2"></i>
                    إدارة العملاء
                </h1>
                <p class="text-muted mb-0">إدارة وتنظيم العملاء</p>
            </div>
            <div>
                <a href="/admin/customers/create" class="btn btn-custom">
                    <i class="fas fa-plus me-2"></i>إضافة عميل جديد
                </a>
            </div>
        </div>
    </div>


    <!-- البحث والفلترة المباشرة -->
    <div class="row mb-4">
        <div class="col-md-4">
            <input type="text" class="form-control" placeholder="البحث في العملاء..." id="searchInput">
        </div>
        <div class="col-md-3">
            <select class="form-select" id="statusFilter">
                <option value="">جميع الحالات</option>
                <option value="active">نشط</option>
                <option value="inactive">غير نشط</option>
                <option value="pending">في الانتظار</option>
            </select>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="cityFilter">
                <option value="">جميع المدن</option>
                <!-- سيتم ملؤها بـ JavaScript -->
            </select>
        </div>
        <div class="col-md-2">
            <button class="btn btn-secondary w-100" onclick="clearFilters()">
                <i class="fas fa-times"></i> مسح
            </button>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead class="thead-dark">
                <tr>
                    <th>
                        <a href="?sortBy=id&sortOrder=<%= (filters.sortBy === 'id' && filters.sortOrder === 'asc') ? 'desc' : 'asc' %>"
                           class="text-white text-decoration-none">
                            ID
                            <% if (filters.sortBy === 'id') { %>
                                <i class="fas fa-sort-<%= filters.sortOrder === 'asc' ? 'up' : 'down' %>"></i>
                            <% } else { %>
                                <i class="fas fa-sort text-muted"></i>
                            <% } %>
                        </a>
                    </th>
                    <th>
                        <a href="?sortBy=name&sortOrder=<%= (filters.sortBy === 'name' && filters.sortOrder === 'asc') ? 'desc' : 'asc' %>"
                           class="text-white text-decoration-none">
                            اسم العميل
                            <% if (filters.sortBy === 'name') { %>
                                <i class="fas fa-sort-<%= filters.sortOrder === 'asc' ? 'up' : 'down' %>"></i>
                            <% } else { %>
                                <i class="fas fa-sort text-muted"></i>
                            <% } %>
                        </a>
                    </th>
                    <th>الصورة</th>
                    <th>رقم الهاتف</th>
                    <th>العنوان</th>
                    <th>المدينة</th>
                    <th>المنطقة</th>
                    <th>الباركود</th>
                    <th>نسبة الخصم</th>
                    <th>الطلبات</th>
                    <th>
                        <a href="?sortBy=status&sortOrder=<%= (filters.sortBy === 'status' && filters.sortOrder === 'asc') ? 'desc' : 'asc' %>"
                           class="text-white text-decoration-none">
                            الحالة
                            <% if (filters.sortBy === 'status') { %>
                                <i class="fas fa-sort-<%= filters.sortOrder === 'asc' ? 'up' : 'down' %>"></i>
                            <% } else { %>
                                <i class="fas fa-sort text-muted"></i>
                            <% } %>
                        </a>
                    </th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <% customers.forEach(customer => { %>
                    <tr class="customer-item"
                        data-name="<%= customer.name || '' %>"
                        data-phone="<%= customer.phoneNumber || '' %>"
                        data-city="<%= customer.city || '' %>"
                        data-region="<%= customer.region || '' %>"
                        data-status="<%= customer.status || '' %>"
                        data-address="<%= customer.address || '' %>">
                        <td><%= customer.id %></td>
                        <td><%= customer.name %></td>
                        <td>
                            <% if (customer.image) { %>
                                <img src="<%= customer.image %>" alt="صورة العميل"
                                     style="width: 40px; height: 40px; object-fit: cover; border-radius: 50%;">
                            <% } else { %>
                                <div style="width: 40px; height: 40px; background: #f8f9fa; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-user text-muted"></i>
                                </div>
                            <% } %>
                        </td>
                        <td><%= customer.phoneNumber || 'غير محدد' %></td>
                        <td>
                            <% if (customer.address) { %>
                                <span title="<%= customer.address %>">
                                    <%= customer.address.length > 25 ? customer.address.substring(0, 25) + '...' : customer.address %>
                                </span>
                                <% if (customer.latitude && customer.longitude) { %>
                                    <br><small class="text-muted">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <%= customer.latitude %>, <%= customer.longitude %>
                                    </small>
                                <% } %>
                            <% } else { %>
                                <span class="text-muted">غير محدد</span>
                            <% } %>
                        </td>
                        <td>
                            <% if (customer.city) { %>
                                <span class="badge bg-primary"><%= customer.city %></span>
                            <% } else { %>
                                <span class="text-muted">غير محدد</span>
                            <% } %>
                        </td>
                        <td>
                            <% if (customer.region) { %>
                                <span class="badge bg-secondary"><%= customer.region %></span>
                            <% } else { %>
                                <span class="text-muted">غير محدد</span>
                            <% } %>
                        </td>
                        <td>
                            <% if (customer.barcode) { %>
                                <code><%= customer.barcode %></code>
                            <% } else { %>
                                <span class="text-muted">غير محدد</span>
                            <% } %>
                        </td>
                        <td>
                            <span class="text-success"><%= customer.discountRate || 0 %>%</span>
                        </td>
                        <td>
                            <span class="badge badge-primary"><%= customer.orders ? customer.orders.length : 0 %></span>
                        </td>
                        <td>
                            <% if (customer.status === 'active') { %>
                                <span class="badge badge-success">نشط</span>
                            <% } else if (customer.status === 'inactive') { %>
                                <span class="badge badge-danger">غير نشط</span>
                            <% } else if (customer.status === 'pending') { %>
                                <span class="badge badge-warning">في الانتظار</span>
                            <% } else { %>
                                <span class="badge badge-secondary">غير محدد</span>
                            <% } %>
                        </td>
                        <td class="action-buttons">
                            <div class="btn-group" role="group">
                                <% if (customer.status !== 'active') { %>
                                    <form action="/admin/customers/<%= customer.id %>/status" method="POST" class="d-inline">
                                        <input type="hidden" name="status" value="active">
                                        <button type="submit" class="btn btn-sm btn-success" title="تفعيل">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </form>
                                <% } %>
                                <a href="/admin/customers/<%= customer.id %>/edit"
                                   class="btn btn-sm btn-warning"
                                   title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="/admin/customers/<%= customer.id %>"
                                   class="btn btn-sm btn-info"
                                   title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <form action="/admin/customers/<%= customer.id %>/delete" method="POST" class="d-inline">
                                    <button type="submit"
                                            class="btn btn-sm btn-danger"
                                            title="حذف"
                                            onclick="return confirm('هل أنت متأكد من حذف هذا العميل؟')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                <% }); %>

                <% if (!customers || customers.length === 0) { %>
                    <tr>
                        <td colspan="13" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-users fa-3x mb-3"></i>
                                <p class="mb-0">لا توجد عملاء مطابقين للبحث</p>
                                <% if (Object.keys(filters || {}).length > 0) { %>
                                    <a href="/admin/customers" class="btn btn-sm btn-primary mt-2">
                                        <i class="fas fa-times"></i> مسح الفلاتر
                                    </a>
                                <% } %>
                            </div>
                        </td>
                    </tr>
                <% } %>
            </tbody>
        </table>
    </div>

    <!-- الـ Pagination الجديد -->
    <%- include('../../partials/pagination', {
        pagination: pagination || null,
        currentUrl: currentUrl || '',
        originalUrl: originalUrl || ''
    }) %>
</div>

<script>
    // البحث والفلترة المباشرة للعملاء
    document.addEventListener('DOMContentLoaded', function() {
        // ملء قائمة المدن
        populateCityFilter();

        // إضافة مستمعي الأحداث
        document.getElementById('searchInput').addEventListener('input', filterCustomers);
        document.getElementById('statusFilter').addEventListener('change', filterCustomers);
        document.getElementById('cityFilter').addEventListener('change', filterCustomers);
    });

    function populateCityFilter() {
        const customers = document.querySelectorAll('.customer-item');
        const cities = new Set();

        customers.forEach(customer => {
            const city = customer.dataset.city;
            if (city && city.trim() !== '') {
                cities.add(city);
            }
        });

        const cityFilter = document.getElementById('cityFilter');
        cities.forEach(city => {
            const option = document.createElement('option');
            option.value = city;
            option.textContent = city;
            cityFilter.appendChild(option);
        });
    }

    function filterCustomers() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const statusFilter = document.getElementById('statusFilter').value;
        const cityFilter = document.getElementById('cityFilter').value;

        const customers = document.querySelectorAll('.customer-item');
        let visibleCount = 0;

        customers.forEach(customer => {
            const name = customer.dataset.name.toLowerCase();
            const phone = customer.dataset.phone.toLowerCase();
            const address = customer.dataset.address.toLowerCase();
            const status = customer.dataset.status;
            const city = customer.dataset.city;

            const matchesSearch = searchTerm === '' ||
                                name.includes(searchTerm) ||
                                phone.includes(searchTerm) ||
                                address.includes(searchTerm);
            const matchesStatus = !statusFilter || status === statusFilter;
            const matchesCity = !cityFilter || city === cityFilter;

            if (matchesSearch && matchesStatus && matchesCity) {
                customer.style.display = '';
                visibleCount++;
            } else {
                customer.style.display = 'none';
            }
        });

        // إظهار رسالة عدم وجود نتائج
        updateNoResultsMessage(visibleCount);
    }

    function updateNoResultsMessage(visibleCount) {
        let noResultsRow = document.getElementById('noResultsRow');

        if (visibleCount === 0) {
            if (!noResultsRow) {
                const tbody = document.querySelector('tbody');
                noResultsRow = document.createElement('tr');
                noResultsRow.id = 'noResultsRow';
                noResultsRow.innerHTML = `
                    <td colspan="13" class="text-center py-4">
                        <div class="text-muted">
                            <i class="fas fa-search fa-3x mb-3"></i>
                            <p class="mb-0">لا توجد نتائج مطابقة لمعايير البحث</p>
                        </div>
                    </td>
                `;
                tbody.appendChild(noResultsRow);
            }
            noResultsRow.style.display = '';
        } else {
            if (noResultsRow) {
                noResultsRow.style.display = 'none';
            }
        }
    }

    function clearFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('cityFilter').value = '';
        filterCustomers();
    }
</script>