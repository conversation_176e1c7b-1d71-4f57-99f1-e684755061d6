<div class="container mt-5">
  <div class="card shadow p-4">
    <div class="text-center mb-4">
      <h3 class="text-success"><i class="fas fa-user me-2"></i> الملف الشخصي</h3>
    </div>

    <div class="row align-items-center">
      <div class="col-md-4 text-center mb-3">
        <% if (driver.image) { %>
          <img src="<%= driver.image %>" class="rounded-circle shadow" width="150" height="150" alt="صورة السائق">
        <% } else { %>
          <img src="/default-avatar.png" class="rounded-circle shadow" width="150" height="150" alt="صورة افتراضية">
        <% } %>
      </div>
      <div class="col-md-8">
        <table class="table table-borderless">
          <tr>
            <th>الاسم:</th>
            <td><%= driver.name %></td>
          </tr>
          <tr>
            <th>رقم الهاتف:</th>
            <td><%= driver.phoneNumber %></td>
          </tr>
          <tr>
            <th>الحالة:</th>
            <td>
              <% if (driver.status === 'active') { %>
                <span class="badge bg-success">مفعل</span>
              <% } else { %>
                <span class="badge bg-secondary">غير مفعل</span>
              <% } %>
            </td>
          </tr>
          <tr>
            <th>تاريخ الإنشاء:</th>
            <td><%= new Date(driver.createdAt).toLocaleDateString() %></td>
          </tr>
        </table>

        <div class="d-flex justify-content-end">
          <a href="/admin/drivers/<%= driver.id %>/edit" class="btn btn-outline-primary">
            <i class="fas fa-edit"></i> تعديل الملف الشخصي
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
