<div class="container-fluid mt-4">
  <div class="row">
    <!-- Notifications Panel -->
    <div class="col-md-8">
      <div class="card">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">
              <i class="fas fa-bell"></i> إشعارات الإدارة
            </h5>
            <div>
              <button class="btn btn-sm btn-outline-primary" onclick="markAllAsRead()">
                <i class="fas fa-check-double"></i> تحديد الكل كمقروء
              </button>
              <button class="btn btn-sm btn-outline-danger" onclick="clearAllNotifications()">
                <i class="fas fa-trash"></i> مسح الكل
              </button>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="list-group list-group-flush">
            <% if (notifications.length === 0) { %>
              <div class="alert alert-info mb-0">
                <i class="fas fa-info-circle"></i> لا توجد إشعارات بعد.
              </div>
            <% } %>
            <% notifications.forEach(notification => { 
                try {
                  notification.data = JSON.parse(notification.data);
                } catch(e) {
                  notification.data = {};
                }
            %>
               <div class="row">
        <% if (notifications.length > 0) { %>
            <% notifications.forEach(notification => { %>
                <div class="col-lg-12">
                    <div class="card h-100 notification-card notification-item <%= !notification.readAt ? 'unread' : '' %> priority-<%= notification.priority %>"
                         data-type="<%= notification.type %>"
                         data-status="<%= notification.readAt ? 'read' : 'unread' %>"
                         data-title="<%= notification.title %>"
                         data-message="<%= notification.message %>">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <span class="badge bg-<%= getTypeColor(notification.type) %> me-2">
                                    <i class="fas fa-<%= getTypeIcon(notification.type) %>"></i>
                                    <%= getTypeLabel(notification.type) %>
                                </span>
                                <span class="badge bg-<%= getPriorityColor(notification.priority) %>">
                                    <%= getPriorityLabel(notification.priority) %>
                                </span>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="/admin/notifications/<%= notification.id %>">
                                        <i class="fas fa-eye"></i> عرض التفاصيل
                                    </a></li>
                                    <% if (!notification.readAt) { %>
                                        <li><a class="dropdown-item" href="#" onclick="markAsRead(<%= notification.id %>)">
                                            <i class="fas fa-check"></i> وضع كمقروء
                                        </a></li>
                                    <% } %>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteNotification(<%= notification.id %>)">
                                        <i class="fas fa-trash"></i> حذف
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="card-body">
                            <h6 class="card-title"><%= notification.title %></h6>
                            <p class="card-text text-muted">
                                <%= notification.message.length > 100 ? notification.message.substring(0, 100) + '...' : notification.message %>
                            </p>

                            <div class="notification-meta">
                                <small class="text-muted d-block">
                                    <i class="fas fa-user"></i>
                                    من: <%= notification.admin ? notification.admin.name : 'النظام' %>
                                </small>
                                <small class="text-muted d-block">
                                    <i class="fas fa-users"></i>
                                    إلى: <%= notification.customer ? notification.customer.name :
                                           notification.admin ? 'المشرف' : 'جميع المستخدمين' %>
                                </small>
                                <small class="text-muted d-block">
                                    <i class="fas fa-clock"></i>
                                    <%= new Date(notification.createdAt).toLocaleString('ar-SA') %>
                                </small>
                                <% if (notification.expiresAt) { %>
                                    <small class="text-warning d-block">
                                        <i class="fas fa-hourglass-end"></i>
                                        ينتهي: <%= new Date(notification.expiresAt).toLocaleString('ar-SA') %>
                                    </small>
                                <% } %>
                            </div>
                        </div>
                        <% if (notification.actionUrl && notification.actionText) { %>
                            <div class="card-footer">
                                <a href="<%= notification.actionUrl %>" class="btn btn-sm btn-primary">
                                    <%= notification.actionText %>
                                </a>
                            </div>
                        <% } %>
                        <% if (!notification.readAt) { %>
                            <div class="unread-indicator"></div>
                        <% } %>
                    </div>
                </div>
            <% }); %>
        <% } else { %>
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد إشعارات</h4>
                    <p class="text-muted">لم يتم العثور على أي إشعارات تطابق معايير البحث</p>
                    <a href="/notifications/create" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إنشاء إشعار جديد
                    </a>
                </div>
            </div>
        <% } %>
    </div>


            <% }); %>
          </div>
        </div>
      </div>
    </div>

    <!-- Notifications Settings -->
    <div class="col-md-4">
      <div class="card mt-3">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-cog"></i> إعدادات الإشعارات
          </h5>
        </div>
        <div class="card-body">
          <div class="form-check form-switch mb-3">
            <input class="form-check-input" type="checkbox" id="enableNotifications" checked>
            <label class="form-check-label" for="enableNotifications">تفعيل الإشعارات</label>
          </div>
          <div class="form-check form-switch mb-3">
            <input class="form-check-input" type="checkbox" id="enableSounds" checked>
            <label class="form-check-label" for="enableSounds">تفعيل الأصوات</label>
          </div>
          <button class="btn btn-outline-primary btn-sm w-100" onclick="testNotification()">
            <i class="fas fa-test-tube"></i> اختبار الإشعارات
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <% if (totalPages > 1) { %>
    <div class="row mt-4">
      <div class="col-md-8">
        <nav>
          <ul class="pagination justify-content-center">
            <% for (let i = 1; i <= totalPages; i++) { %>
              <li class="page-item <%= i === currentPage ? 'active' : '' %>">
                <a class="page-link" href="?page=<%= i %>"><%= i %></a>
              </li>
            <% } %>
          </ul>
        </nav>
      </div>
    </div>
  <% } %>
  <%
function getTypeColor(type) {
    const colors = {
        'info': 'info',
        'success': 'success',
        'warning': 'warning',
        'error': 'danger',
        'order': 'primary',
        'promotion': 'success',
        'system': 'secondary'
    };
    return colors[type] || 'secondary';
}

function getTypeIcon(type) {
    const icons = {
        'info': 'info-circle',
        'success': 'check-circle',
        'warning': 'exclamation-triangle',
        'error': 'times-circle',
        'order': 'shopping-cart',
        'promotion': 'tag',
        'system': 'cog'
    };
    return icons[type] || 'bell';
}

function getTypeLabel(type) {
    const labels = {
        'info': 'معلومات',
        'success': 'نجاح',
        'warning': 'تحذير',
        'error': 'خطأ',
        'order': 'طلب',
        'promotion': 'عرض',
        'system': 'نظام'
    };
    return labels[type] || type;
}

function getPriorityColor(priority) {
    const colors = {
        'low': 'secondary',
        'normal': 'info',
        'high': 'warning',
        'urgent': 'danger'
    };
    return colors[priority] || 'secondary';
}

function getPriorityLabel(priority) {
    const labels = {
        'low': 'منخفضة',
        'normal': 'عادية',
        'high': 'عالية',
        'urgent': 'عاجلة'
    };
    return labels[priority] || priority;
}
%>
</div>

<script src="/js/notification-actions.js"></script>
