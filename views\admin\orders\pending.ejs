<style>
 .page-header {
        background: var(--white);
        padding: 2rem;
        border-radius: 16px;
        box-shadow: var(--shadow);
        margin-bottom: 2rem;
    }
</style>

<div class="container-fluid">
         <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-2">
                    <i class="fas fa-clock text-primary me-2"></i>
                الطلبات في حالة الانتظار
                </h1>
                <p class="text-muted mb-0">الطلبات التي تحتاج للمراجعة والموافقة</p>
            </div>
            <div class="btn-group">
            <a href="/admin/orders" class="btn btn-outline-primary">
                <i class="fas fa-list"></i> جميع الطلبات
            </a>
            <button type="button" class="btn btn-success" onclick="approveAllPending()" 
                    <%= orders.length === 0 ? 'disabled' : '' %>>
                <i class="fas fa-check-double"></i> موافقة على الكل
            </button>
        </div>
        </div>
    </div>
 

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">إجمالي الطلبات المعلقة</h5>
                            <h2 class="mb-0"><%= stats.totalPending %></h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">طلبات اليوم</h5>
                            <h2 class="mb-0"><%= stats.todayPending %></h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-day fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">القيمة الإجمالية</h5>
                            <h2 class="mb-0"><%= stats.totalValue.toFixed(2) %> ل.س</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

       <!-- البحث والفلترة المباشرة -->
        <div class="row mb-4">
            <div class="col-md-4">
                <input type="text" class="form-control" placeholder="البحث في الطلبات..." id="searchInput">
            </div>
            <div class="col-md-3">
                <select class="form-select" id="customerFilter">
                    
                </select>
            </div>
            <div class="col-md-3">
                <input type="date" class="form-control" id="dateFilter" title="تصفية حسب التاريخ">
            </div>
            <div class="col-md-2">
                <button class="btn btn-secondary w-100" onclick="clearFilters()">
                    <i class="fas fa-times"></i> مسح
                </button>
            </div>
        </div>

    <!-- Orders Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>قائمة الطلبات المعلقة
            </h5>
            <div class="d-flex align-items-center">
                <span class="text-muted me-3">عرض <%= orders.length %> من <%= pagination.totalItems %></span>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-secondary" onclick="refreshPage()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <% if (orders && orders.length > 0) { %>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th>رقم الطلب</th>
                                <th>العميل</th>
                                <th>عدد المنتجات</th>
                                <th>المبلغ الإجمالي</th>
                                <th>تاريخ الطلب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% orders.forEach(order => { %>
                                <tr id="order-<%= order.id %>" 
                                    class="order-item" 
                                    data-id="<%= order.id %>" 
                                    data-customer="<%= order.customer?.name || '' %>" 
                                    data-phone="<%= order.customer?.phoneNumber || '' %>" 
                                    data-status="<%= order.status %>" 
                                    data-date="<%= new Date(order.createdAt).toISOString().slice(0, 10) %>">
                                    <td>
                                        <input type="checkbox" class="order-checkbox" value="<%= order.id %>">
                                    </td>
                                    <td>
                                        <strong>#<%= order.id %></strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><%= order.customer?.name || 'غير محدد' %></strong>
                                            <% if (order.customer?.phoneNumber) { %>
                                                <br><small class="text-muted">
                                                    <i class="fas fa-phone"></i> <%= order.customer.phoneNumber %>
                                                </small>
                                            <% } %>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <%= order.orderDetails?.length || 0 %> منتج
                                        </span>
                                    </td>
                                    <td>
                                        <strong class="text-success">
                                            <%= parseFloat(order.totalPrice || 0).toFixed(2) %> ل.س
                                        </strong>
                                    </td>
                                    <td>
                                        <small>
                                            <%= new Date(order.createdAt).toLocaleDateString('ar-SA') %>
                                            <br>
                                            <%= new Date(order.createdAt).toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'}) %>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="/admin/orders/<%= order.id %>" class="btn btn-outline-info" 
                                               title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="/admin/deliveries/create?orderId=<%= order.id %>" class="btn btn-outline-warning" title="تعيين سائق">
                                                <i class="fas fa-motorcycle"></i>
                                            </a>
                                            <button class="btn btn-success" onclick="approveOrder(<%= order.id %>)" 
                                                    title="موافقة">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-danger" onclick="rejectOrder(<%= order.id %>)" 
                                                    title="رفض">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
            <% } else { %>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد طلبات في حالة الانتظار</h5>
                    <p class="text-muted">جميع الطلبات تم معالجتها أو لا توجد طلبات جديدة.</p>
                    <a href="/admin/orders" class="btn btn-primary">
                        <i class="fas fa-list"></i> عرض جميع الطلبات
                    </a>
                </div>
            <% } %>
        </div>
    </div>

    <!-- Pagination -->
    <% if (pagination.totalPages > 1) { %>
        <%- include('../../partials/pagination', { pagination, currentUrl }) %>
    <% } %>
</div>

<style>
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.5rem;
    }
    
    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
    }
    
    .badge {
        font-size: 0.75em;
    }
    
    .text-success {
        color: var(--primary-color) !important;
    }
    
    .bg-warning {
        background-color: #ffc107 !important;
    }
    
    .bg-info {
        background-color: #17a2b8 !important;
    }
    
    .bg-success {
        background-color: var(--primary-color) !important;
    }
</style>

<script>
// تحديد/إلغاء تحديد جميع الطلبات
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.order-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// تحديث حالة checkbox الرئيسي
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.order-checkbox');
    const selectAll = document.getElementById('selectAll');

    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.order-checkbox:checked').length;
            selectAll.checked = checkedCount === checkboxes.length;
            selectAll.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
        });
    });
});

function approveOrder(orderId) {
    if (confirm('هل أنت متأكد من الموافقة على هذا الطلب؟')) {
        fetch(`/admin/orders/${orderId}/approve`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم الموافقة على الطلب بنجاح');
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء الموافقة على الطلب');
        });
    }
}

function rejectOrder(orderId) {
    const reason = prompt('أدخل سبب الرفض:');
    if (reason) {
        fetch(`/admin/orders/${orderId}/reject`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ reason })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم رفض الطلب');
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء رفض الطلب');
        });
    }
}

// موافقة على جميع الطلبات المحددة
async function approveAllPending() {
    const checkedBoxes = document.querySelectorAll('.order-checkbox:checked');

    if (checkedBoxes.length === 0) {
        showAlert('يرجى تحديد طلب واحد على الأقل', 'warning');
        return;
    }

    if (!confirm(`هل أنت متأكد من الموافقة على ${checkedBoxes.length} طلب؟`)) {
        return;
    }

    const orderIds = Array.from(checkedBoxes).map(cb => cb.value);
    let successCount = 0;
    let errorCount = 0;

    for (const orderId of orderIds) {
        try {
            const response = await fetch(`/admin/orders/${orderId}/approve`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const result = await response.json();

            if (result.success) {
                successCount++;
                const row = document.getElementById(`order-${orderId}`);
                if (row) {
                    row.style.transition = 'opacity 0.3s';
                    row.style.opacity = '0.5';
                    setTimeout(() => row.remove(), 300);
                }
            } else {
                errorCount++;
            }
        } catch (error) {
            errorCount++;
        }
    }

    if (successCount > 0) {
        showAlert(`تم الموافقة على ${successCount} طلب بنجاح`, 'success');
        setTimeout(updateStats, 500);
    }

    if (errorCount > 0) {
        showAlert(`فشل في معالجة ${errorCount} طلب`, 'error');
    }
}

// تحديث الإحصائيات
function updateStats() {
    const remainingOrders = document.querySelectorAll('tbody tr').length;

    // تحديث عدد الطلبات المعلقة
    const totalPendingElement = document.querySelector('.bg-warning .card-body h2');
    if (totalPendingElement) {
        totalPendingElement.textContent = remainingOrders;
    }

    // إذا لم تعد هناك طلبات، أظهر رسالة فارغة
    if (remainingOrders === 0) {
        setTimeout(() => {
            location.reload();
        }, 1000);
    }
}

// تحديث الصفحة
function refreshPage() {
    location.reload();
}

// عرض التنبيهات
function showAlert(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // إضافة التنبيه في أعلى الصفحة
    const container = document.querySelector('.container-fluid');
    const alertDiv = document.createElement('div');
    alertDiv.innerHTML = alertHtml;
    container.insertBefore(alertDiv.firstElementChild, container.firstElementChild);

    // إزالة التنبيه تلقائ<|im_start|>ه بعد 5 ثوان
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

// تحديث تلقائي كل 30 ثانية
setInterval(() => {
    const currentCount = document.querySelectorAll('tbody tr').length;
    if (currentCount > 0) {
        // تحقق من وجود طلبات جديدة بدون إعادة تحميل كامل
        fetch('/admin/orders/pending?ajax=1')
            .then(response => response.json())
            .then(data => {
                if (data.newOrdersCount > currentCount) {
                    showAlert('يوجد طلبات جديدة! انقر لتحديث الصفحة.', 'info');
                }
            })
            .catch(console.error);
    }
}, 30000);

// البحث والفلترة المباشرة للطلبات
document.addEventListener('DOMContentLoaded', function() {

    populateCustomerFilter();

    document.getElementById('searchInput').addEventListener('input', filterOrders);
    document.getElementById('customerFilter').addEventListener('change', filterOrders);
    document.getElementById('dateFilter').addEventListener('change', filterOrders);
});

function populateCustomerFilter() {
    const orders = document.querySelectorAll('.order-item');
    const customers = new Set();

    orders.forEach(order => {
        const customer = order.dataset.customer;
        if (customer && customer.trim() !== '') {
            customers.add(customer);
        }
    });

    const customerFilter = document.getElementById('customerFilter');
    customerFilter.innerHTML = '<option value="">كل العملاء</option>'; // إضافة خيار "الكل"

    customers.forEach(customer => {
        const option = document.createElement('option');
        option.value = customer.toLowerCase();
        option.textContent = customer;
        customerFilter.appendChild(option);
    });
}

function filterOrders() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const customerFilter = document.getElementById('customerFilter').value.toLowerCase();
    const dateFilter = document.getElementById('dateFilter').value;

    const orders = document.querySelectorAll('.order-item');
    let visibleCount = 0;

    orders.forEach(order => {
        const orderId = order.dataset.id.toLowerCase();
        const customer = order.dataset.customer.toLowerCase();
        const phone = order.dataset.phone.toLowerCase();
        const status = order.dataset.status;
        const date = order.dataset.date;

        const matchesSearch = searchTerm === '' ||
                              orderId.includes(searchTerm) ||
                              customer.includes(searchTerm) ||
                              phone.includes(searchTerm);
        const matchesCustomer = !customerFilter || customer === customerFilter;
        const matchesDate = !dateFilter || date === dateFilter;

        if (matchesSearch && matchesCustomer && matchesDate) {
            order.style.display = '';
            visibleCount++;
        } else {
            order.style.display = 'none';
        }
    });

    updateNoResultsMessage(visibleCount);
}

function updateNoResultsMessage(visibleCount) {
    let noResultsRow = document.getElementById('noResultsRow');

    if (visibleCount === 0) {
        if (!noResultsRow) {
            const tbody = document.querySelector('tbody');
            noResultsRow = document.createElement('tr');
            noResultsRow.id = 'noResultsRow';
            noResultsRow.innerHTML = `
                <td colspan="6" class="text-center py-4">
                    <div class="text-muted">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <p class="mb-0">لا توجد طلبات مطابقة لمعايير البحث</p>
                    </div>
                </td>
            `;
            tbody.appendChild(noResultsRow);
        }
        noResultsRow.style.display = '';
    } else {
        if (noResultsRow) {
            noResultsRow.style.display = 'none';
        }
    }
}

function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('customerFilter').value = '';
    document.getElementById('dateFilter').value = '';
    filterOrders();
}
</script>