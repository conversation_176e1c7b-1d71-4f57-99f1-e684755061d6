<div class="container mt-5">
  <div class="card shadow p-4">
    <h3 class="text-center mb-4 text-success">
      <i class="fas fa-tags me-2"></i>
      إضافة عرض جديد للمنتج: <%= product.name %>
    </h3>

    <form action="/admin/products/<%= product.id %>/offer" method="POST" enctype="multipart/form-data">
      <!-- صورة العرض -->
      <div class="mb-3">
        <label for="image" class="form-label fw-bold">صورة العرض</label>
        <input type="file" name="image" id="image" class="form-control" accept="image/*" required>
      </div>

      <!-- قيمة الخصم -->
      <div class="mb-3">
        <label for="discount_value" class="form-label fw-bold">قيمة الخصم</label>
        <input type="number" step="0.01" name="discount_value" id="discount_value" class="form-control" required>
      </div>

      <!-- السعر بعد الخصم -->
      <div class="mb-3">
        <label for="price_after_discount" class="form-label fw-bold">السعر بعد الخصم</label>
        <input type="number" step="0.01" name="price_after_discount" id="price_after_discount" class="form-control" required>
      </div>

      <!-- تاريخ البداية -->
      <div class="mb-3">
        <label for="start_date" class="form-label fw-bold">تاريخ بدء العرض</label>
        <input type="date" name="start_date" id="start_date" class="form-control" required>
      </div>

      <!-- تاريخ النهاية -->
      <div class="mb-3">
        <label for="end_date" class="form-label fw-bold">تاريخ انتهاء العرض</label>
        <input type="date" name="end_date" id="end_date" class="form-control" required>
      </div>

      <!-- الحالة -->
      <div class="mb-4">
        <label for="status" class="form-label fw-bold">حالة العرض</label>
        <select name="status" id="status" class="form-select">
          <option value="active">مفعل</option>
          <option value="inactive">غير مفعل</option>
        </select>
      </div>

      <!-- أزرار الإجراء -->
      <div class="d-flex justify-content-between">
        <a href="/admin/products" class="btn btn-secondary">
          <i class="fas fa-arrow-right"></i> العودة
        </a>
        <button type="submit" class="btn btn-success">
          <i class="fas fa-plus"></i> حفظ العرض
        </button>
      </div>
    </form>
  </div>
</div>
<script>
  // السعر الأصلي للمنتج (بدل القيمة دي بالسعر الحقيقي للمنتج)
  const originalPrice = <%= product.price %>;

  const discountInput = document.getElementById('discount_value');
  const priceAfterDiscountInput = document.getElementById('price_after_discount');

  // لما يتغير قيمة الخصم
  discountInput.addEventListener('input', () => {
    const discount = parseFloat(discountInput.value);
    if (!isNaN(discount) && discount >= 0 && discount <= originalPrice) {
      // نحسب السعر بعد الخصم
      const newPrice = (originalPrice - discount).toFixed(2);
      priceAfterDiscountInput.value = newPrice;
    } else {
      priceAfterDiscountInput.value = '';
    }
  });

  // لما يتغير السعر بعد الخصم
  priceAfterDiscountInput.addEventListener('input', () => {
    const priceAfterDiscount = parseFloat(priceAfterDiscountInput.value);
    if (!isNaN(priceAfterDiscount) && priceAfterDiscount >= 0 && priceAfterDiscount <= originalPrice) {
      // نحسب الخصم = السعر الأصلي - السعر بعد الخصم
      const discount = (originalPrice - priceAfterDiscount).toFixed(2);
      discountInput.value = discount;
    } else {
      discountInput.value = '';
    }
  });
</script>

