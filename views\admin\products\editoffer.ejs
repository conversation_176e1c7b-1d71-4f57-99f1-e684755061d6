<div class="container mt-5">
  <div class="card shadow p-4">
    <h3 class="text-center mb-4 text-primary">
      <i class="fas fa-edit me-2"></i>
      تعديل العرض للمنتج: <%= product.name %>
    </h3>

    <form action="/admin/products/<%= offer.id %>/offer/edit" method="POST" enctype="multipart/form-data">
      <!-- صورة العرض -->
      <div class="mb-3">
        <label for="image" class="form-label fw-bold">صورة العرض</label>
        <% if (offer.image) { %>
          <div class="mb-2">
            <img src="<%= offer.image %>" alt="صورة العرض الحالية" class="img-thumbnail" style="max-width: 150px;">
          </div>
        <% } %>
        <input type="file" name="image" id="image" class="form-control" accept="image/*">
      </div>

      <!-- قيمة الخصم -->
      <div class="mb-3">
        <label for="discount_value" class="form-label fw-bold">قيمة الخصم</label>
        <input type="number" step="0.01" name="discount_value" id="discount_value" class="form-control" value="<%= offer.discount_value %>" required>
      </div>

      <!-- السعر بعد الخصم -->
      <div class="mb-3">
        <label for="price_after_discount" class="form-label fw-bold">السعر بعد الخصم</label>
        <input type="number" step="0.01" name="price_after_discount" id="price_after_discount" class="form-control" value="<%= offer.price_after_discount %>" required>
      </div>

      <!-- تاريخ البداية -->
      <div class="mb-3">
        <label for="start_date" class="form-label fw-bold">تاريخ بدء العرض</label>
        <input type="date" name="start_date" id="start_date" class="form-control" value="<%= offer.start_date.toISOString().split('T')[0] %>" required>
      </div>

      <!-- تاريخ النهاية -->
      <div class="mb-3">
        <label for="end_date" class="form-label fw-bold">تاريخ انتهاء العرض</label>
        <input type="date" name="end_date" id="end_date" class="form-control" value="<%= offer.end_date.toISOString().split('T')[0] %>" required>
      </div>

      <!-- الحالة -->
      <div class="mb-4">
        <label for="status" class="form-label fw-bold">حالة العرض</label>
        <select name="status" id="status" class="form-select">
          <option value="active" <%= offer.status === 'active' ? 'selected' : '' %>>مفعل</option>
          <option value="inactive" <%= offer.status === 'inactive' ? 'selected' : '' %>>غير مفعل</option>
        </select>
      </div>

      <!-- أزرار الإجراء -->
      <div class="d-flex justify-content-between">
        <a href="/admin/products" class="btn btn-secondary">
          <i class="fas fa-arrow-right"></i> العودة
        </a>
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-save"></i> حفظ التعديلات
        </button>
      </div>
    </form>
  </div>
</div>

<script>
  const originalPrice = <%= product.price %>;

  const discountInput = document.getElementById('discount_value');
  const priceAfterDiscountInput = document.getElementById('price_after_discount');

  discountInput.addEventListener('input', () => {
    const discount = parseFloat(discountInput.value);
    if (!isNaN(discount) && discount >= 0 && discount <= originalPrice) {
      const newPrice = (originalPrice - discount).toFixed(2);
      priceAfterDiscountInput.value = newPrice;
    } else {
      priceAfterDiscountInput.value = '';
    }
  });

  priceAfterDiscountInput.addEventListener('input', () => {
    const priceAfterDiscount = parseFloat(priceAfterDiscountInput.value);
    if (!isNaN(priceAfterDiscount) && priceAfterDiscount >= 0 && priceAfterDiscount <= originalPrice) {
      const discount = (originalPrice - priceAfterDiscount).toFixed(2);
      discountInput.value = discount;
    } else {
      discountInput.value = '';
    }
  });
</script>
