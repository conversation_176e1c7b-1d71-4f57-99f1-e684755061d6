<style>
    body {
        font-family: 'Cairo', sans-serif;
        background-color: #f5f7fa;
    }

    .offer-card {
        background: #fff;
        border-radius: 15px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        padding: 1rem;
        margin-bottom: 1.5rem;
        transition: transform 0.2s;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .offer-card:hover {
        transform: translateY(-5px);
    }

    .offer-image {
        object-fit: contain;
        border-radius: 10px;
        max-height: 200px;
        width: 100%;
        margin-bottom: 1rem;
    }

    .badge-custom {
        background-color: #8FBC8F;
        color: #fff;
        padding: 0.3rem 0.7rem;
        border-radius: 1rem;
        font-size: 0.8rem;
    }

    .bg-primary {
        background-color: #598ddb !important;
        width: fit-content;
    }

    .price-tag {
        background-color: #e74c3c;
        color: white;
        padding: 0.3rem 0.7rem;
        border-radius: 1rem;
        font-weight: bold;
        font-size: 0.9rem;
    }

    .d-none-important {
        display: none !important;
    }
</style>

<div class="container py-5">
    <h1 class="mb-4 text-center text-success">
        <i class="fas fa-tags me-2"></i> العروض الحالية
    </h1>

    <!-- فلاتر البحث -->
    <div class="row mb-4">
        <div class="col-md-3 mb-2">
            <input type="text" class="form-control" placeholder="بحث بالاسم أو الوصف" id="searchInput">
        </div>
        <div class="col-md-2 mb-2">
            <select class="form-select" id="categoryFilter">
                <option value="">جميع الفئات</option>
            </select>
        </div>
        <div class="col-md-2 mb-2">
            <select class="form-select" id="discountFilter">
                <option value="">جميع الخصومات</option>
                <option value="10-25">10% - 25%</option>
                <option value="25-50">25% - 50%</option>
                <option value="50-75">50% - 75%</option>
                <option value="75-100">75% - 100%</option>
            </select>
        </div>
        <div class="col-md-2 mb-2">
            <select class="form-select" id="statusFilter">
                <option value="">جميع الحالات</option>
                <option value="active">مفعل</option>
                <option value="inactive">غير مفعل</option>
            </select>
        </div>
        <div class="col-md-2 mb-2">
            <input type="date" class="form-control" id="dateFilter">
        </div>
        <div class="col-md-1 mb-2">
            <button class="btn btn-secondary w-100" onclick="clearFilters()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <div class="row" id="offersContainer">
        <% if (products && products.length > 0) { %>
            <% products.forEach(offer => {
                const product = offer.product;
                const price = parseFloat(product.price) || 0;
                const discount = parseFloat(offer.discount_value || 0);
                const priceAfter = parseFloat(offer.price_after_discount || price);
                const discountPercent = discount && price ? (((price - priceAfter ) / price) * 100).toFixed(1) : 0;
            %>
            <div class="col-md-6 col-lg-4 d-flex offer-item"
                data-name="<%= product.name %>"
                data-description="<%= product.description || '' %>"
                data-category="<%= product.category ? product.category.name : '' %>"
                data-price="<%= price %>"
                data-discount="<%= discount %>"
                data-status="<%= offer.status %>"
                data-start="<%= offer.start_date ? offer.start_date.toISOString().split('T')[0] : '' %>"
                data-end="<%= offer.end_date ? offer.end_date.toISOString().split('T')[0] : '' %>">
                <div class="offer-card w-100 d-flex flex-column">
                    <% if (offer.image) { %>
                        <img src="<%= offer.image %>" class="offer-image" alt="صورة العرض لـ <%= product.name %>">
                    <% } else { %>
                        <img src="/images/no-image.png" class="offer-image" alt="لا توجد صورة">
                    <% } %>

                    <h5><%= product.name %></h5>
                    <p class="text-muted small mb-2"><%= product.description || 'لا يوجد وصف' %></p>

                    <div class="d-flex flex-column gap-1 mt-auto">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="price-tag text-decoration-line-through text-muted"><%= price.toLocaleString() %> ل.س</span>
                            <span class="badge bg-success">بعد الخصم: <%= priceAfter.toLocaleString() %> ل.س</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-warning text-dark">الحسم: <%= discount.toLocaleString() %> ل.س</span>
                        </div>
                    </div>

                    <% if (product.category) { %>
                        <small class="badge bg-primary mt-2"> <%= product.category.name %></small>
                    <% } %>

                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <a href="/admin/products/<%= offer.id %>/offer/edit" class="btn btn-sm btn-outline-success">
                            <i class="fas fa-edit me-1"></i>تعديل
                        </a>
                        <a href="/admin/products/<%= product.id %>/delete-offer?offerId=<%= offer.id %>" 
                            class="btn btn-sm btn-outline-danger"
                            onclick="return confirm('هل أنت متأكد من حذف هذا العرض؟');">
                            <i class="fas fa-trash-alt"></i> حذف العرض
                        </a>
                    </div>
                </div>
            </div>
            <% }) %>
        <% } else { %>
            <div class="col-12 text-center py-5">
                <i class="fas fa-box fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد عروض حالياً</h4>
            </div>
        <% } %>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        populateCategoryFilter();

        document.getElementById('searchInput').addEventListener('input', filterOffers);
        document.getElementById('categoryFilter').addEventListener('change', filterOffers);
        document.getElementById('discountFilter').addEventListener('change', filterOffers);
        document.getElementById('statusFilter').addEventListener('change', filterOffers);
        document.getElementById('dateFilter').addEventListener('change', filterOffers);

        filterOffers();
    });

    function populateCategoryFilter() {
        const offers = document.querySelectorAll('.offer-item');
        const categories = new Set();

        offers.forEach(offer => {
            const category = offer.dataset.category?.trim();
            if (category) categories.add(category);
        });

        const categoryFilter = document.getElementById('categoryFilter');
        while (categoryFilter.options.length > 1) {
            categoryFilter.remove(1);
        }

        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category;
            categoryFilter.appendChild(option);
        });
    }

    function filterOffers() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const categoryFilter = document.getElementById('categoryFilter').value;
        const discountFilter = document.getElementById('discountFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        const dateFilter = document.getElementById('dateFilter').value;

        const offers = document.querySelectorAll('.offer-item');
        let visibleCount = 0;

        offers.forEach(offer => {
            const name = offer.dataset.name.toLowerCase();
            const description = offer.dataset.description.toLowerCase();
            const category = offer.dataset.category;
            const discount = parseFloat(offer.dataset.discount) || 0;
            const status = offer.dataset.status;
            const start = offer.dataset.start;
            const end = offer.dataset.end;

            const matchesSearch = searchTerm === '' || name.includes(searchTerm) || description.includes(searchTerm);
            const matchesCategory = !categoryFilter || category?.trim().toLowerCase() === categoryFilter.trim().toLowerCase();
            const matchesDiscount = !discountFilter || checkDiscountRange(discount, discountFilter);
            const matchesStatus = !statusFilter || status === statusFilter;

            let matchesDate = true;
            if (dateFilter) {
                matchesDate = (!start || !end) ? false : (dateFilter >= start && dateFilter <= end);
            }

            if (matchesSearch && matchesCategory && matchesDiscount && matchesStatus && matchesDate) {
                offer.classList.remove('d-none-important');
                visibleCount++;
            } else {
                offer.classList.add('d-none-important');
            }
        });

        updateNoResultsMessage(visibleCount);
    }

    function checkDiscountRange(discount, range) {
        const [min, max] = range.split('-').map(Number);
        return !isNaN(discount) && discount >= min && discount <= max;
    }

    function updateNoResultsMessage(visibleCount) {
        let noResultsDiv = document.getElementById('noResultsDiv');

        if (visibleCount === 0) {
            if (!noResultsDiv) {
                const container = document.getElementById('offersContainer');
                noResultsDiv = document.createElement('div');
                noResultsDiv.id = 'noResultsDiv';
                noResultsDiv.className = 'col-12 text-center py-5';
                noResultsDiv.innerHTML = `
                    <i class="fas fa-search fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد عروض مطابقة لمعايير البحث</h4>
                `;
                container.appendChild(noResultsDiv);
            }
            noResultsDiv.style.display = '';
        } else {
            if (noResultsDiv) noResultsDiv.style.display = 'none';
        }
    }

    function clearFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('categoryFilter').value = '';
        document.getElementById('discountFilter').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('dateFilter').value = '';
        filterOffers();
    }
</script>
